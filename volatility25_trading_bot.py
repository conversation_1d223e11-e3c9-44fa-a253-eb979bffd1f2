"""
Volatility 25 Index Trading Bot
===============================

Market: Volatility 25 (1s) Index - Deriv Synthetic Index
Timeframe: M5 (5-minute candles)
Capital: $100
Lot Size: 0.005

Strategy:
- Stochastic Oscillator (%K=14, %D=3, Slowing=3)
- EMA 50
- Dynamic Support/Resistance zones
- Pin Bar & Engulfing pattern confirmation

Risk Management:
- SL: 200 points
- TP: 300 points (RR 1:1.5)
- Max daily loss: $5
- Max concurrent trades: 1
"""

import asyncio
import websockets
import json
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import talib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DerivTradingBot:
    """
    Automated trading bot for Volatility 25 Index using Deriv API
    """
    
    def __init__(self, app_id: str, api_token: str):
        """
        Initialize the trading bot
        
        Args:
            app_id: Deriv app ID
            api_token: Deriv API token
        """
        self.app_id = app_id
        self.api_token = api_token
        self.ws_url = f"wss://ws.binaryws.com/websockets/v3?app_id={app_id}"
        self.websocket = None
        
        # Trading parameters
        self.symbol = "R_25"  # Volatility 25 Index
        self.timeframe = "5m"  # 5-minute candles
        self.lot_size = 0.005
        self.stop_loss_points = 200
        self.take_profit_points = 300
        self.max_daily_loss = 5.0
        self.max_concurrent_trades = 1
        
        # Strategy parameters
        self.stoch_k_period = 14
        self.stoch_d_period = 3
        self.stoch_slowing = 3
        self.stoch_overbought = 90
        self.stoch_oversold = 10
        self.ema_period = 50
        self.swing_lookback = 50
        
        # Trading state
        self.current_trades = []
        self.daily_pnl = 0.0
        self.last_trade_time = None
        self.candle_data = pd.DataFrame()
        self.support_levels = []
        self.resistance_levels = []
        
    async def connect(self):
        """Establish WebSocket connection to Deriv API"""
        try:
            self.websocket = await websockets.connect(self.ws_url)
            logger.info("Connected to Deriv API")
            
            # Authorize the connection
            await self.authorize()
            
            # Subscribe to candle data
            await self.subscribe_to_candles()
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            raise
    
    async def authorize(self):
        """Authorize the API connection"""
        auth_request = {
            "authorize": self.api_token
        }
        await self.websocket.send(json.dumps(auth_request))
        response = await self.websocket.recv()
        auth_data = json.loads(response)
        
        if auth_data.get("error"):
            raise Exception(f"Authorization failed: {auth_data['error']['message']}")
        
        logger.info("API authorization successful")
    
    async def subscribe_to_candles(self):
        """Subscribe to 5-minute candle data for Volatility 25"""
        subscribe_request = {
            "ticks_history": self.symbol,
            "adjust_start_time": 1,
            "count": 100,  # Get last 100 candles for initial data
            "end": "latest",
            "start": 1,
            "style": "candles",
            "granularity": 300,  # 5 minutes = 300 seconds
            "subscribe": 1
        }
        await self.websocket.send(json.dumps(subscribe_request))
        logger.info("Subscribed to candle data")

    def get_data(self, candle_response: Dict) -> bool:
        """
        Process incoming candle data and update DataFrame

        Args:
            candle_response: WebSocket response containing candle data

        Returns:
            bool: True if new candle data was processed
        """
        try:
            if "candles" in candle_response:
                candles = candle_response["candles"]

                # Convert to DataFrame
                df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                df = df.set_index('timestamp')

                # Convert to float
                for col in ['open', 'high', 'low', 'close']:
                    df[col] = df[col].astype(float)

                self.candle_data = df
                logger.info(f"Updated candle data: {len(df)} candles")
                return True

            elif "ohlc" in candle_response:
                # Real-time candle update
                ohlc = candle_response["ohlc"]
                timestamp = pd.to_datetime(ohlc["open_time"], unit='s')

                new_candle = pd.DataFrame({
                    'open': [float(ohlc["open"])],
                    'high': [float(ohlc["high"])],
                    'low': [float(ohlc["low"])],
                    'close': [float(ohlc["close"])]
                }, index=[timestamp])

                # Update or append candle
                if timestamp in self.candle_data.index:
                    self.candle_data.loc[timestamp] = new_candle.loc[timestamp]
                else:
                    self.candle_data = pd.concat([self.candle_data, new_candle])
                    self.candle_data = self.candle_data.tail(100)  # Keep last 100 candles

                return True

        except Exception as e:
            logger.error(f"Error processing candle data: {e}")
            return False

        return False

    def calculate_indicators(self) -> Dict:
        """
        Calculate all technical indicators

        Returns:
            Dict: Dictionary containing all indicator values
        """
        if len(self.candle_data) < max(self.ema_period, self.stoch_k_period):
            return {}

        try:
            # Get OHLC data
            high = self.candle_data['high'].values
            low = self.candle_data['low'].values
            close = self.candle_data['close'].values

            # Calculate EMA 50
            ema_50 = talib.EMA(close, timeperiod=self.ema_period)

            # Calculate Stochastic Oscillator
            stoch_k, stoch_d = talib.STOCH(
                high, low, close,
                fastk_period=self.stoch_k_period,
                slowk_period=self.stoch_slowing,
                slowd_period=self.stoch_d_period
            )

            # Get current values (last completed candle)
            current_ema = ema_50[-2] if len(ema_50) > 1 else None
            current_stoch_k = stoch_k[-2] if len(stoch_k) > 1 else None
            current_stoch_d = stoch_d[-2] if len(stoch_d) > 1 else None
            previous_stoch_k = stoch_k[-3] if len(stoch_k) > 2 else None

            current_price = close[-2] if len(close) > 1 else None

            return {
                'ema_50': current_ema,
                'stoch_k': current_stoch_k,
                'stoch_d': current_stoch_d,
                'prev_stoch_k': previous_stoch_k,
                'current_price': current_price,
                'stoch_k_cross_up': (previous_stoch_k < self.stoch_oversold and
                                    current_stoch_k > self.stoch_oversold) if previous_stoch_k and current_stoch_k else False,
                'stoch_k_cross_down': (previous_stoch_k > self.stoch_overbought and
                                     current_stoch_k < self.stoch_overbought) if previous_stoch_k and current_stoch_k else False
            }

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}

    def find_support_resistance_zones(self):
        """
        Identify dynamic support and resistance levels from recent swing highs/lows
        """
        if len(self.candle_data) < self.swing_lookback:
            return

        try:
            # Get recent data for swing analysis
            recent_data = self.candle_data.tail(self.swing_lookback)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Find swing highs (local maxima)
            swing_highs = []
            for i in range(2, len(highs) - 2):
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    swing_highs.append(highs[i])

            # Find swing lows (local minima)
            swing_lows = []
            for i in range(2, len(lows) - 2):
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    swing_lows.append(lows[i])

            # Keep last 3 swing highs and lows
            self.resistance_levels = sorted(swing_highs, reverse=True)[:3]
            self.support_levels = sorted(swing_lows)[:3]

            logger.info(f"Support levels: {self.support_levels}")
            logger.info(f"Resistance levels: {self.resistance_levels}")

        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")

    def detect_candle_patterns(self) -> Dict:
        """
        Detect Pin Bar and Engulfing candle patterns

        Returns:
            Dict: Pattern detection results
        """
        if len(self.candle_data) < 2:
            return {'pin_bar_bullish': False, 'pin_bar_bearish': False,
                   'engulfing_bullish': False, 'engulfing_bearish': False}

        try:
            # Get last two candles
            current = self.candle_data.iloc[-1]
            previous = self.candle_data.iloc[-2]

            # Calculate candle properties
            current_body = abs(current['close'] - current['open'])
            current_range = current['high'] - current['low']
            current_upper_wick = current['high'] - max(current['open'], current['close'])
            current_lower_wick = min(current['open'], current['close']) - current['low']



            # Pin Bar Detection
            # Pin bar criteria: small body (< 1/3 of range), long wick (> 2/3 of range)
            pin_bar_bullish = False
            pin_bar_bearish = False

            if current_range > 0:
                body_ratio = current_body / current_range
                lower_wick_ratio = current_lower_wick / current_range
                upper_wick_ratio = current_upper_wick / current_range

                # Bullish Pin Bar: long lower wick, small body, short upper wick
                if (body_ratio < 0.33 and lower_wick_ratio > 0.66 and
                    current['close'] > current['open']):
                    pin_bar_bullish = True

                # Bearish Pin Bar: long upper wick, small body, short lower wick
                if (body_ratio < 0.33 and upper_wick_ratio > 0.66 and
                    current['close'] < current['open']):
                    pin_bar_bearish = True

            # Engulfing Pattern Detection
            engulfing_bullish = False
            engulfing_bearish = False

            # Bullish Engulfing: previous red candle, current green candle that engulfs previous
            if (previous['close'] < previous['open'] and  # Previous bearish
                current['close'] > current['open'] and   # Current bullish
                current['open'] < previous['close'] and  # Current opens below previous close
                current['close'] > previous['open']):    # Current closes above previous open
                engulfing_bullish = True

            # Bearish Engulfing: previous green candle, current red candle that engulfs previous
            if (previous['close'] > previous['open'] and  # Previous bullish
                current['close'] < current['open'] and   # Current bearish
                current['open'] > previous['close'] and  # Current opens above previous close
                current['close'] < previous['open']):    # Current closes below previous open
                engulfing_bearish = True

            return {
                'pin_bar_bullish': pin_bar_bullish,
                'pin_bar_bearish': pin_bar_bearish,
                'engulfing_bullish': engulfing_bullish,
                'engulfing_bearish': engulfing_bearish
            }

        except Exception as e:
            logger.error(f"Error detecting candle patterns: {e}")
            return {'pin_bar_bullish': False, 'pin_bar_bearish': False,
                   'engulfing_bullish': False, 'engulfing_bearish': False}

    def check_near_zone(self, price: float, zone_type: str) -> bool:
        """
        Check if price is near support or resistance zone

        Args:
            price: Current price
            zone_type: 'support' or 'resistance'

        Returns:
            bool: True if price is near the zone
        """
        tolerance = getattr(self, 'zone_tolerance', 0.001)  # 0.1% tolerance for zone proximity

        if zone_type == 'support' and self.support_levels:
            for level in self.support_levels:
                if abs(price - level) / level <= tolerance:
                    return True
        elif zone_type == 'resistance' and self.resistance_levels:
            for level in self.resistance_levels:
                if abs(price - level) / level <= tolerance:
                    return True

        return False

    def analyze_trade_setup(self) -> Optional[str]:
        """
        Analyze current market conditions for trade setup

        Returns:
            str: 'BUY', 'SELL', or None
        """
        # Check if we can trade (daily loss limit, max trades)
        if self.daily_pnl <= -self.max_daily_loss:
            logger.info("Daily loss limit reached. No new trades.")
            return None

        if len(self.current_trades) >= self.max_concurrent_trades:
            logger.info("Maximum concurrent trades reached.")
            return None

        # Get indicators and patterns
        indicators = self.calculate_indicators()
        if not indicators:
            return None

        patterns = self.detect_candle_patterns()
        self.find_support_resistance_zones()

        current_price = indicators.get('current_price')
        ema_50 = indicators.get('ema_50')

        if not current_price or not ema_50:
            return None

        # BUY Setup Analysis
        buy_conditions = [
            current_price > ema_50,  # Price above EMA 50 (uptrend)
            indicators.get('stoch_k_cross_up', False),  # Stochastic crosses up from oversold
            self.check_near_zone(current_price, 'support'),  # Near support zone
            patterns['pin_bar_bullish'] or patterns['engulfing_bullish']  # Bullish pattern
        ]

        # SELL Setup Analysis
        sell_conditions = [
            current_price < ema_50,  # Price below EMA 50 (downtrend)
            indicators.get('stoch_k_cross_down', False),  # Stochastic crosses down from overbought
            self.check_near_zone(current_price, 'resistance'),  # Near resistance zone
            patterns['pin_bar_bearish'] or patterns['engulfing_bearish']  # Bearish pattern
        ]

        # Log analysis
        logger.info(f"BUY conditions: {buy_conditions} (All: {all(buy_conditions)})")
        logger.info(f"SELL conditions: {sell_conditions} (All: {all(sell_conditions)})")

        if all(buy_conditions):
            return 'BUY'
        elif all(sell_conditions):
            return 'SELL'

        return None

    async def place_order(self, direction: str, current_price: float) -> bool:
        """
        Place a trading order with stop loss and take profit

        Args:
            direction: 'BUY' or 'SELL'
            current_price: Current market price

        Returns:
            bool: True if order placed successfully
        """
        try:
            # Calculate SL and TP levels
            if direction == 'BUY':
                stop_loss = current_price - (self.stop_loss_points * 0.00001)  # Convert points to price
                take_profit = current_price + (self.take_profit_points * 0.00001)
                contract_type = "CALL"
            else:  # SELL
                stop_loss = current_price + (self.stop_loss_points * 0.00001)
                take_profit = current_price - (self.take_profit_points * 0.00001)
                contract_type = "PUT"

            # Create order request
            order_request = {
                "buy": 1,
                "price": self.lot_size,  # For binary options, this is the stake amount
                "parameters": {
                    "contract_type": contract_type,
                    "symbol": self.symbol,
                    "duration": 5,  # 5 minutes
                    "duration_unit": "m",
                    "amount": self.lot_size,
                    "barrier": current_price
                }
            }

            await self.websocket.send(json.dumps(order_request))
            response = await self.websocket.recv()
            order_data = json.loads(response)

            if order_data.get("error"):
                logger.error(f"Order placement failed: {order_data['error']['message']}")
                return False

            # Log successful trade
            trade_info = {
                'direction': direction,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'lot_size': self.lot_size,
                'timestamp': datetime.now(),
                'contract_id': order_data.get('buy', {}).get('contract_id')
            }

            self.current_trades.append(trade_info)
            self.last_trade_time = datetime.now()

            logger.info(f"✅ {direction} order placed successfully!")
            logger.info(f"Entry: {current_price:.5f}, SL: {stop_loss:.5f}, TP: {take_profit:.5f}")

            return True

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return False

    def manage_risk(self):
        """
        Monitor and manage existing trades for risk management
        """
        # Reset daily P&L at start of new day
        current_date = datetime.now().date()
        if hasattr(self, 'last_reset_date') and self.last_reset_date != current_date:
            self.daily_pnl = 0.0
            logger.info("Daily P&L reset for new trading day")

        self.last_reset_date = current_date

        # Check if daily loss limit is reached
        if self.daily_pnl <= -self.max_daily_loss:
            logger.warning(f"⚠️ Daily loss limit reached: ${self.daily_pnl:.2f}")
            return

        # Log current status
        logger.info(f"Current trades: {len(self.current_trades)}")
        logger.info(f"Daily P&L: ${self.daily_pnl:.2f}")

    async def run_bot(self):
        """
        Main bot execution loop
        """
        logger.info("🚀 Starting Volatility 25 Trading Bot")

        try:
            await self.connect()

            while True:
                try:
                    # Wait for WebSocket message
                    response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
                    data = json.loads(response)

                    # Process candle data
                    if self.get_data(data):
                        # Manage existing trades and risk
                        self.manage_risk()

                        # Check for new trade opportunities
                        if len(self.candle_data) >= self.ema_period:
                            trade_signal = self.analyze_trade_setup()

                            if trade_signal:
                                current_price = self.candle_data['close'].iloc[-1]
                                await self.place_order(trade_signal, current_price)

                except asyncio.TimeoutError:
                    # Send ping to keep connection alive
                    ping_request = {"ping": 1}
                    await self.websocket.send(json.dumps(ping_request))

                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"Bot execution failed: {e}")
        finally:
            if self.websocket:
                await self.websocket.close()

# Configuration and main execution
async def main():
    """
    Main function to run the trading bot
    """
    # ⚠️ IMPORTANT: Replace with your actual Deriv API credentials
    APP_ID = "YOUR_DERIV_APP_ID"  # Get from https://app.deriv.com/account/api-token
    API_TOKEN = "YOUR_DERIV_API_TOKEN"  # Get from https://app.deriv.com/account/api-token

    # Validate credentials
    if APP_ID == "YOUR_DERIV_APP_ID" or API_TOKEN == "YOUR_DERIV_API_TOKEN":
        logger.error("❌ Please set your Deriv API credentials in the script!")
        logger.error("Get them from: https://app.deriv.com/account/api-token")
        return

    # Create and run the bot
    bot = DerivTradingBot(APP_ID, API_TOKEN)
    await bot.run_bot()

if __name__ == "__main__":
    # Install required packages if not already installed
    try:
        import talib
    except ImportError:
        logger.error("❌ TA-Lib not installed. Install with: pip install TA-Lib")
        exit(1)

    try:
        import websockets
    except ImportError:
        logger.error("❌ websockets not installed. Install with: pip install websockets")
        exit(1)

    # Run the bot
    asyncio.run(main())
