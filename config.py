"""
Configuration file for Volatility 25 Trading Bot
================================================

Instructions:
1. Get your Deriv API credentials from: https://app.deriv.com/account/api-token
2. Replace the placeholder values below with your actual credentials
3. Adjust trading parameters as needed
"""

# ⚠️ IMPORTANT: Replace with your actual Deriv API credentials
DERIV_APP_ID = ""
DERIV_API_TOKEN = "2nEWZETWoNIdnIu"

# Trading Parameters
TRADING_CONFIG = {
    "symbol": "R_25",  # Volatility 25 Index
    "timeframe": "5m",  # 5-minute candles
    "lot_size": 0.005,
    "stop_loss_points": 200,
    "take_profit_points": 300,
    "max_daily_loss": 5.0,  # Maximum daily loss in USD
    "max_concurrent_trades": 1
}

# Strategy Parameters
STRATEGY_CONFIG = {
    "stoch_k_period": 14,
    "stoch_d_period": 3,
    "stoch_slowing": 3,
    "stoch_overbought": 90,
    "stoch_oversold": 10,
    "ema_period": 50,
    "swing_lookback": 50  # Number of candles to look back for swing highs/lows
}

# Risk Management
RISK_CONFIG = {
    "zone_tolerance": 0.001,  # 0.1% tolerance for support/resistance zones
    "pin_bar_body_ratio": 0.33,  # Pin bar body must be < 33% of total range
    "pin_bar_wick_ratio": 0.66   # Pin bar wick must be > 66% of total range
}
