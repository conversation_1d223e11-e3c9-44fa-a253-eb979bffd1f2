"""
Volatility 25 Trading Bot Runner
===============================

This script runs the trading bot with configuration from config.py
"""

import asyncio
import logging
from volatility25_trading_bot import DerivTradingBot
from config import DERIV_APP_ID, DERIV_API_TOKEN, TRADING_CONFIG, STRATEGY_CONFIG, RISK_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConfiguredTradingBot(DerivTradingBot):
    """
    Trading bot with configuration from config.py
    """
    
    def __init__(self):
        super().__init__(DERIV_APP_ID, DERIV_API_TOKEN)
        
        # Apply trading configuration
        self.symbol = TRADING_CONFIG["symbol"]
        self.timeframe = TRADING_CONFIG["timeframe"]
        self.lot_size = TRADING_CONFIG["lot_size"]
        self.stop_loss_points = TRADING_CONFIG["stop_loss_points"]
        self.take_profit_points = TRADING_CONFIG["take_profit_points"]
        self.max_daily_loss = TRADING_CONFIG["max_daily_loss"]
        self.max_concurrent_trades = TRADING_CONFIG["max_concurrent_trades"]
        
        # Apply strategy configuration
        self.stoch_k_period = STRATEGY_CONFIG["stoch_k_period"]
        self.stoch_d_period = STRATEGY_CONFIG["stoch_d_period"]
        self.stoch_slowing = STRATEGY_CONFIG["stoch_slowing"]
        self.stoch_overbought = STRATEGY_CONFIG["stoch_overbought"]
        self.stoch_oversold = STRATEGY_CONFIG["stoch_oversold"]
        self.ema_period = STRATEGY_CONFIG["ema_period"]
        self.swing_lookback = STRATEGY_CONFIG["swing_lookback"]
        
        # Apply risk configuration
        self.zone_tolerance = RISK_CONFIG["zone_tolerance"]
        self.pin_bar_body_ratio = RISK_CONFIG["pin_bar_body_ratio"]
        self.pin_bar_wick_ratio = RISK_CONFIG["pin_bar_wick_ratio"]

async def main():
    """
    Main function to run the configured trading bot
    """
    # Validate credentials
    if DERIV_APP_ID == "YOUR_DERIV_APP_ID" or DERIV_API_TOKEN == "YOUR_DERIV_API_TOKEN":
        logger.error("❌ Please set your Deriv API credentials in config.py!")
        logger.error("Get them from: https://app.deriv.com/account/api-token")
        return
    
    logger.info("🚀 Starting Configured Volatility 25 Trading Bot")
    logger.info(f"📊 Trading Parameters:")
    logger.info(f"   Symbol: {TRADING_CONFIG['symbol']}")
    logger.info(f"   Lot Size: {TRADING_CONFIG['lot_size']}")
    logger.info(f"   Stop Loss: {TRADING_CONFIG['stop_loss_points']} points")
    logger.info(f"   Take Profit: {TRADING_CONFIG['take_profit_points']} points")
    logger.info(f"   Max Daily Loss: ${TRADING_CONFIG['max_daily_loss']}")
    
    # Create and run the bot
    bot = ConfiguredTradingBot()
    await bot.run_bot()

if __name__ == "__main__":
    # Check required packages
    try:
        import talib
        import websockets
        import pandas
        import numpy
    except ImportError as e:
        logger.error(f"❌ Missing required package: {e}")
        logger.error("Install with: pip install -r requirements.txt")
        exit(1)
    
    # Run the bot
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Bot crashed: {e}")
