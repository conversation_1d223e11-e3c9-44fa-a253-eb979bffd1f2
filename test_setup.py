"""
Test script to validate trading bot setup
=========================================

This script checks if all dependencies are installed correctly
and validates the configuration.
"""

import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_dependencies():
    """Test if all required dependencies are installed"""
    logger.info("🔍 Testing dependencies...")
    
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('websockets', 'websockets'),
        ('talib', 'TA-Lib'),
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            logger.info(f"✅ {package_name} - OK")
        except ImportError:
            logger.error(f"❌ {package_name} - MISSING")
            missing_packages.append(pip_name)
    
    if missing_packages:
        logger.error(f"\n📦 Install missing packages with:")
        logger.error(f"pip install {' '.join(missing_packages)}")
        return False
    
    logger.info("✅ All dependencies installed correctly!")
    return True

def test_configuration():
    """Test if configuration is properly set"""
    logger.info("\n🔧 Testing configuration...")
    
    try:
        from config import DERIV_APP_ID, DERIV_API_TOKEN, TRADING_CONFIG, STRATEGY_CONFIG
        
        # Check API credentials
        if DERIV_APP_ID == "YOUR_DERIV_APP_ID":
            logger.error("❌ DERIV_APP_ID not configured in config.py")
            return False
        else:
            logger.info("✅ DERIV_APP_ID - Configured")
        
        if DERIV_API_TOKEN == "YOUR_DERIV_API_TOKEN":
            logger.error("❌ DERIV_API_TOKEN not configured in config.py")
            return False
        else:
            logger.info("✅ DERIV_API_TOKEN - Configured")
        
        # Validate trading config
        required_keys = ['symbol', 'lot_size', 'stop_loss_points', 'take_profit_points']
        for key in required_keys:
            if key in TRADING_CONFIG:
                logger.info(f"✅ {key}: {TRADING_CONFIG[key]}")
            else:
                logger.error(f"❌ Missing {key} in TRADING_CONFIG")
                return False
        
        logger.info("✅ Configuration looks good!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Error importing config: {e}")
        return False

def test_bot_import():
    """Test if the bot can be imported"""
    logger.info("\n🤖 Testing bot import...")
    
    try:
        from volatility25_trading_bot import DerivTradingBot
        logger.info("✅ Bot class imported successfully")
        
        # Test basic initialization (without API connection)
        bot = DerivTradingBot("test_app_id", "test_token")
        logger.info("✅ Bot initialization successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error importing/initializing bot: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Starting Trading Bot Setup Test\n")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Configuration", test_configuration),
        ("Bot Import", test_bot_import)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        try:
            if not test_func():
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {test_name} test failed with error: {e}")
            all_passed = False
    
    logger.info("\n" + "="*50)
    if all_passed:
        logger.info("🎉 All tests passed! Bot is ready to run.")
        logger.info("📝 Next steps:")
        logger.info("   1. Ensure you have a Deriv demo/live account")
        logger.info("   2. Run: python bot_runner.py")
        logger.info("   3. Monitor the logs in trading_bot.log")
    else:
        logger.error("❌ Some tests failed. Please fix the issues above.")
        logger.error("📚 Check README.md for setup instructions")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
