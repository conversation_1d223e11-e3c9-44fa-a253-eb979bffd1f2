# Volatility 25 Trading Bot

An automated trading bot for Deriv's Volatility 25 Index using advanced technical analysis and risk management.

## 🎯 Trading Strategy

### Market & Setup
- **Market**: Volatility 25 (1s) Index (Deriv Synthetic Index)
- **Timeframe**: M5 (5-minute candles)
- **Capital**: $100
- **Lot Size**: 0.005

### Technical Indicators
1. **Stochastic Oscillator**
   - %K: 14 periods
   - %D: 3 periods
   - Slowing: 3
   - Overbought: 90
   - Oversold: 10

2. **EMA 50** - Trend direction filter

3. **Dynamic Support/Resistance Zones**
   - Based on last 3 swing highs/lows in past 50 candles

### Entry Conditions

#### 🟢 BUY Setup
- ✅ Price above EMA 50 (uptrend)
- ✅ Stochastic %K crosses up from below 10 (oversold)
- ✅ Price near recent support zone
- ✅ Bullish candle pattern (Pin Bar OR Engulfing)

#### 🔴 SELL Setup
- ✅ Price below EMA 50 (downtrend)
- ✅ Stochastic %K crosses down from above 90 (overbought)
- ✅ Price near recent resistance zone
- ✅ Bearish candle pattern (Pin Bar OR Engulfing)

### Risk Management
- **Stop Loss**: 200 points
- **Take Profit**: 300 points (Risk:Reward = 1:1.5)
- **Max Daily Loss**: $5
- **Max Concurrent Trades**: 1
- **Position Size**: 0.005 lots

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Get Deriv API Credentials
1. Go to [Deriv API Token Page](https://app.deriv.com/account/api-token)
2. Create a new API token with trading permissions
3. Note down your App ID and API Token

### 3. Configure the Bot
Edit `config.py` and replace:
```python
DERIV_APP_ID = "YOUR_DERIV_APP_ID"
DERIV_API_TOKEN = "YOUR_DERIV_API_TOKEN"
```

### 4. Run the Bot
```bash
python bot_runner.py
```

## 📁 File Structure

```
├── volatility25_trading_bot.py  # Main bot class with all trading logic
├── bot_runner.py               # Bot runner with configuration
├── config.py                   # Configuration file (EDIT THIS)
├── requirements.txt            # Python dependencies
├── README.md                   # This file
└── trading_bot.log            # Log file (created when running)
```

## 🔧 Configuration Options

### Trading Parameters (`config.py`)
```python
TRADING_CONFIG = {
    "symbol": "R_25",              # Volatility 25 Index
    "lot_size": 0.005,             # Position size
    "stop_loss_points": 200,       # SL in points
    "take_profit_points": 300,     # TP in points
    "max_daily_loss": 5.0,         # Max daily loss ($)
    "max_concurrent_trades": 1     # Max open trades
}
```

### Strategy Parameters
```python
STRATEGY_CONFIG = {
    "stoch_k_period": 14,          # Stochastic %K period
    "stoch_overbought": 90,        # Overbought level
    "stoch_oversold": 10,          # Oversold level
    "ema_period": 50,              # EMA period
    "swing_lookback": 50           # Swing analysis period
}
```

## 📊 Key Features

### 🎯 Pattern Recognition
- **Pin Bar Detection**: Identifies rejection candles with long wicks
- **Engulfing Patterns**: Detects momentum reversal candles
- **Dynamic Zones**: Automatically finds support/resistance levels

### 🛡️ Risk Management
- **Daily Loss Limit**: Stops trading when daily loss reaches $5
- **Position Sizing**: Fixed 0.005 lot size for consistent risk
- **Stop Loss/Take Profit**: Automatic SL/TP on every trade

### 📈 Real-time Analysis
- **Live Data**: WebSocket connection for real-time price updates
- **5-Minute Candles**: Analyzes M5 timeframe for signals
- **Continuous Monitoring**: 24/7 market scanning

## 🔍 How It Works

### 1. Data Collection (`get_data()`)
- Connects to Deriv WebSocket API
- Subscribes to Volatility 25 Index candle data
- Maintains rolling 100-candle dataset

### 2. Technical Analysis (`calculate_indicators()`)
- Calculates EMA 50 for trend direction
- Computes Stochastic Oscillator values
- Identifies crossover signals

### 3. Pattern Detection (`detect_candle_patterns()`)
- Scans for Pin Bar formations
- Identifies Engulfing patterns
- Validates pattern quality

### 4. Zone Analysis (`find_support_resistance_zones()`)
- Finds recent swing highs and lows
- Creates dynamic support/resistance levels
- Checks price proximity to zones

### 5. Trade Execution (`place_order()`)
- Validates all entry conditions
- Places orders with SL/TP
- Logs trade details

### 6. Risk Management (`manage_risk()`)
- Monitors daily P&L
- Enforces position limits
- Tracks trade performance

## 📝 Logging

The bot creates detailed logs in `trading_bot.log`:
- Trade entries and exits
- Signal analysis
- Error messages
- Performance metrics

## ⚠️ Important Notes

1. **Demo Account**: Test on demo account first
2. **API Limits**: Respect Deriv API rate limits
3. **Market Hours**: Volatility indices trade 24/7
4. **Risk Warning**: Trading involves risk of loss
5. **Backtesting**: Consider backtesting before live trading

## 🛠️ Troubleshooting

### Common Issues
1. **TA-Lib Installation**: May require additional setup on Windows
2. **WebSocket Disconnection**: Bot auto-reconnects
3. **API Errors**: Check credentials and permissions

### Support
- Check logs in `trading_bot.log`
- Verify API credentials
- Ensure stable internet connection

## 📈 Performance Monitoring

The bot logs:
- Entry/exit prices
- Win/loss ratio
- Daily P&L
- Signal frequency
- Pattern success rate

---

**Disclaimer**: This bot is for educational purposes. Trading involves risk. Always test thoroughly before using real money.
